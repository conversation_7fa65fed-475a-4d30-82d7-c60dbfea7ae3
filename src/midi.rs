use anyhow::{anyhow, Result};
use midir::{MidiOutput, MidiOutputConnection};
use std::collections::HashMap;
use std::time::{Duration, Instant};

pub struct MidiOutputDevice {
    connection: Option<MidiOutputConnection>,
    active_notes: HashMap<u16, Instant>, // ノート番号とチャンネルの組み合わせ、およびノートオン時刻のマップ
    note_duration: Duration, // ノートの最小持続時間
}

impl MidiOutputDevice {
    pub fn new(note_duration_ms: u64) -> Self {
        Self {
            connection: None,
            active_notes: HashMap::new(),
            note_duration: Duration::from_millis(note_duration_ms),
        }
    }

    pub fn initialize(&mut self) -> Result<()> {
        let midi_out = MidiOutput::new("realtime-audio2midi")?;

        // 利用可能なMIDIポートを表示
        let ports = midi_out.ports();
        if ports.is_empty() {
            return Err(anyhow!("利用可能なMIDI出力ポートがありません"));
        }

        println!("利用可能なMIDI出力ポート:");
        for (i, port) in ports.iter().enumerate() {
            println!("{}: {}", i, midi_out.port_name(port)?);
        }

        // ポートの選択
        println!("使用するMIDIポートの番号を入力してください:");
        println!("（Microsoft GS Wavetable Synthは遅延が大きいことがあります。他のMIDIデバイスがあれば試してください）");
        let mut input = String::new();
        std::io::stdin().read_line(&mut input)?;

        let port_index = input.trim().parse::<usize>().unwrap_or(0);
        let port_index = if port_index >= ports.len() { 0 } else { port_index };

        let port = &ports[port_index];
        println!("選択されたMIDIポート: {}", midi_out.port_name(port)?);

        let connection = midi_out.connect(port, "midi-connection")?;
        self.connection = Some(connection);

        Ok(())
    }

    pub fn note_on(&mut self, note: u8, velocity: u8) -> Result<()> {
        self.note_on_channel(note, velocity, 0)
    }

    pub fn note_on_channel(&mut self, note: u8, velocity: u8, channel: u8) -> Result<()> {
        if let Some(conn) = &mut self.connection {
            // チャンネルは0-15の範囲に制限
            let channel = channel.min(15);

            // ノートオンメッセージを送信 (0x90 + channel)
            let message = [0x90 | channel, note, velocity];
            // println!("MIDI送信: ノートオン - ノート: {}, ベロシティ: {}, チャンネル: {}", note, velocity, channel);
            conn.send(&message)?;

            // アクティブノートに追加 (チャンネル情報も含める)
            let note_key = (note as u16) << 8 | (channel as u16);
            self.active_notes.insert(note_key, Instant::now());

            Ok(())
        } else {
            Err(anyhow!("MIDI接続が初期化されていません"))
        }
    }

    pub fn note_off(&mut self, note: u8) -> Result<()> {
        self.note_off_channel(note, 0)
    }

    pub fn note_off_channel(&mut self, note: u8, channel: u8) -> Result<()> {
        if let Some(conn) = &mut self.connection {
            // チャンネルは0-15の範囲に制限
            let channel = channel.min(15);

            // ノートオフメッセージを送信 (0x80 + channel)
            let message = [0x80 | channel, note, 0];
            // println!("MIDI送信: ノートオフ - ノート: {}, チャンネル: {}", note, channel);
            conn.send(&message)?;

            // アクティブノートから削除
            let note_key = (note as u16) << 8 | (channel as u16);
            self.active_notes.remove(&note_key);

            Ok(())
        } else {
            Err(anyhow!("MIDI接続が初期化されていません"))
        }
    }

    pub fn update(&mut self) -> Result<()> {
        // 最小持続時間を超えたノートをオフにする
        let now = Instant::now();

        // すべてのアクティブノートを確認
        let notes_to_off: Vec<(u8, u8)> = self
            .active_notes
            .iter()
            .filter(|&(_, &time)| now.duration_since(time) >= self.note_duration)
            .map(|(&note_key, _)| {
                // ノートキーからノート番号とチャンネルを抽出
                let note = (note_key >> 8) as u8;
                let channel = (note_key & 0x00FF) as u8;
                (note, channel)
            })
            .collect();

        // ノートをオフにする
        for (note, channel) in notes_to_off {
            self.note_off_channel(note, channel)?;
        }

        Ok(())
    }

    pub fn all_notes_off(&mut self) -> Result<()> {
        // すべてのチャンネルのすべてのノートをオフにする
        for channel in 0..16 {
            self.all_notes_off_channel(channel)?;
        }
        Ok(())
    }

    pub fn all_notes_off_channel(&mut self, channel: u8) -> Result<()> {
        if let Some(conn) = &mut self.connection {
            // チャンネルは0-15の範囲に制限
            let channel = channel.min(15);

            // すべてのノートをオフにする
            for note in 0..128 {
                let message = [0x80 | channel, note, 0];
                conn.send(&message)?;
            }

            // このチャンネルのアクティブノートをクリア
            self.active_notes.retain(|&k, _| (k & 0x00FF) as u8 != channel);

            Ok(())
        } else {
            Err(anyhow!("MIDI接続が初期化されていません"))
        }
    }

    // ピッチベンドを送信するメソッド
    // value: -8192〜8191の範囲（0が中央、負の値が下方向、正の値が上方向）
    pub fn pitch_bend_channel(&mut self, value: i16, channel: u8) -> Result<()> {
        if let Some(conn) = &mut self.connection {
            // チャンネルは0-15の範囲に制限
            let channel = channel.min(15);

            // ピッチベンド値を0〜16383の範囲に変換（8192が中央）
            let bend_value = (value + 8192).max(0).min(16383) as u16;

            // ピッチベンドメッセージを送信 (0xE0 + channel)
            // 最下位7ビットと次の7ビットに分割
            let lsb = (bend_value & 0x7F) as u8;
            let msb = ((bend_value >> 7) & 0x7F) as u8;

            let message = [0xE0 | channel, lsb, msb];
            // println!("MIDI送信: ピッチベンド - 値: {}, チャンネル: {}", value, channel);
            conn.send(&message)?;

            Ok(())
        } else {
            Err(anyhow!("MIDI接続が初期化されていません"))
        }
    }
}
