use anyhow::Result;
use apodize::hanning_iter;
use rustfft::{FftPlanner, num_complex::Complex};
use std::sync::Arc;

pub struct FFTProcessor {
    fft_size: usize,
    window: Vec<f32>,
    fft: Arc<dyn rustfft::Fft<f32>>,
}

impl FFTProcessor {
    pub fn new(fft_size: usize) -> Self {
        // FFTサイズを2のべき乗に調整
        let power_of_two_size = if fft_size.is_power_of_two() {
            fft_size
        } else {
            // 次の2のべき乗に切り上げる
            let mut size = 1;
            while size < fft_size {
                size *= 2;
            }
            // println!("FFTサイズを2のべき乗に調整: {} -> {}", fft_size, size);
            size
        };

        // ハニング窓を作成
        let window: Vec<f32> = hanning_iter(power_of_two_size).map(|x| x as f32).collect();

        // FFTプランナーを作成
        let mut planner = FftPlanner::new();
        let fft = planner.plan_fft_forward(power_of_two_size);

        Self {
            fft_size: power_of_two_size,
            window,
            fft,
        }
    }

    pub fn process(&self, input: &[f32]) -> Result<Vec<f32>> {
        // 入力データのサイズがFFTサイズと一致しない場合は調整
        let adjusted_input: Vec<f32> = if input.len() != self.fft_size {
            // println!("入力データのサイズ({})をFFTサイズ({})に調整します", input.len(), self.fft_size);

            if input.len() < self.fft_size {
                // 入力が小さい場合はゼロパディング
                let mut padded = input.to_vec();
                padded.resize(self.fft_size, 0.0);
                padded
            } else {
                // 入力が大きい場合は切り詰め
                input[0..self.fft_size].to_vec()
            }
        } else {
            input.to_vec()
        };

        // 窓関数を適用し、複素数に変換
        let mut buffer: Vec<Complex<f32>> = adjusted_input
            .iter()
            .zip(self.window.iter())
            .map(|(&sample, &window)| Complex { re: sample * window, im: 0.0 })
            .collect();

        // FFTを実行
        self.fft.process(&mut buffer);

        // 振幅スペクトルを計算（絶対値の2乗）
        let spectrum: Vec<f32> = buffer
            .iter()
            .map(|c| (c.re * c.re + c.im * c.im).sqrt())
            .collect();

        Ok(spectrum)
    }

    // 複素数FFT結果を取得するメソッド（円表現用）
    pub fn process_complex(&self, input: &[f32]) -> Result<Vec<Complex<f32>>> {
        // 入力データのサイズがFFTサイズと一致しない場合は調整
        let adjusted_input: Vec<f32> = if input.len() != self.fft_size {
            if input.len() < self.fft_size {
                // 入力が小さい場合はゼロパディング
                let mut padded = input.to_vec();
                padded.resize(self.fft_size, 0.0);
                padded
            } else {
                // 入力が大きい場合は切り詰め
                input[0..self.fft_size].to_vec()
            }
        } else {
            input.to_vec()
        };

        // 窓関数を適用し、複素数に変換
        let mut buffer: Vec<Complex<f32>> = adjusted_input
            .iter()
            .zip(self.window.iter())
            .map(|(&sample, &window)| Complex { re: sample * window, im: 0.0 })
            .collect();

        // FFTを実行
        self.fft.process(&mut buffer);

        Ok(buffer)
    }

    // フーリエ級数の円表現用のデータを計算
    // 各周波数成分を円の半径と位相として返す
    pub fn get_fourier_circles(&self, complex_spectrum: &[Complex<f32>], max_circles: usize) -> Vec<(f32, f32)> {
        // 結果を格納する配列（半径, 位相）のペア
        let mut circles = Vec::new();

        // スペクトルの半分だけを使用（ナイキスト周波数まで）
        let half_spectrum = &complex_spectrum[0..self.fft_size / 2];

        // 各周波数成分について
        for (i, c) in half_spectrum.iter().enumerate() {
            // 振幅（半径）と位相を計算
            let radius = (c.re * c.re + c.im * c.im).sqrt();
            let phase = c.im.atan2(c.re);

            // 振幅が0に近い場合はスキップ
            if radius < 0.001 {
                continue;
            }

            circles.push((radius, phase));

            // 最大円数に達したら終了
            if circles.len() >= max_circles {
                break;
            }
        }

        // 振幅（半径）で降順ソート
        circles.sort_by(|a, b| b.0.partial_cmp(&a.0).unwrap());

        circles
    }

    pub fn frequency_to_bin(&self, frequency: f32, sample_rate: u32) -> usize {
        // 周波数をFFTビンのインデックスに変換
        let bin = (frequency * self.fft_size as f32 / sample_rate as f32).round() as usize;
        bin.min(self.fft_size / 2) // ナイキスト周波数を超えないようにする
    }

    pub fn bin_to_frequency(&self, bin: usize, sample_rate: u32) -> f32 {
        // FFTビンのインデックスを周波数に変換
        bin as f32 * sample_rate as f32 / self.fft_size as f32
    }

    pub fn get_dominant_frequencies(&self, spectrum: &[f32], sample_rate: u32, threshold: f32, max_peaks: usize) -> Vec<(f32, f32)> {
        let mut peaks = Vec::new();

        // スペクトルの半分だけを使用（ナイキスト周波数まで）
        let half_spectrum = &spectrum[0..self.fft_size / 2];

        // ローカルピークを見つける
        for i in 1..half_spectrum.len() - 1 {
            if half_spectrum[i] > half_spectrum[i - 1] &&
               half_spectrum[i] > half_spectrum[i + 1] &&
               half_spectrum[i] > threshold {

                // 周波数と振幅のペアを追加
                let frequency = self.bin_to_frequency(i, sample_rate);
                peaks.push((frequency, half_spectrum[i]));
            }
        }

        // 振幅で降順ソート
        peaks.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());

        // 最大ピーク数に制限
        peaks.truncate(max_peaks);

        peaks
    }

    // スペクトルを指定された数の周波数帯域に分割し、各帯域の平均振幅を計算
    pub fn get_frequency_bands(&self, spectrum: &[f32], sample_rate: u32, num_bands: usize) -> Vec<f32> {
        // スペクトルの半分だけを使用（ナイキスト周波数まで）
        let half_spectrum = &spectrum[0..self.fft_size / 2];
        let nyquist_freq = sample_rate as f32 / 2.0;

        // 各帯域の結果を格納する配列
        let mut band_amplitudes = vec![0.0; num_bands];
        let mut band_counts = vec![0; num_bands];

        // 各周波数ビンがどの帯域に属するかを計算
        for (i, &amplitude) in half_spectrum.iter().enumerate() {
            // ビンの周波数を計算
            let frequency = self.bin_to_frequency(i, sample_rate);

            // 周波数が属する帯域を計算（対数スケールで分割）
            // 人間の聴覚は低周波数の方が敏感なので、対数スケールが適切
            let band_index = if frequency > 0.0 {
                let log_freq = (frequency / nyquist_freq).ln();
                let log_min = 1.0f32.ln(); // 最小周波数（1Hz）の対数
                let log_max = nyquist_freq.ln(); // 最大周波数の対数

                // 対数スケールでの位置を計算（0.0〜1.0）
                let normalized_position = (log_freq - log_min) / (log_max - log_min);

                // 帯域インデックスに変換
                (normalized_position * num_bands as f32).floor() as usize
            } else {
                0 // 0Hzは最初の帯域に
            };

            // 帯域インデックスが範囲内であることを確認
            if band_index < num_bands {
                band_amplitudes[band_index] += amplitude;
                band_counts[band_index] += 1;
            }
        }

        // 各帯域の平均振幅を計算
        for i in 0..num_bands {
            if band_counts[i] > 0 {
                band_amplitudes[i] /= band_counts[i] as f32;
            }
        }

        band_amplitudes
    }
}
