use pitch_detection::detector::{PitchDetector, mcleod::<PERSON><PERSON><PERSON><PERSON>etector};
use std::collections::HashMap;

// 音階を表す構造体
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash)]
pub struct Note {
    pub letter: char,
    pub accidental: Option<&'static str>,
    pub octave: u8,
}

pub struct NoteDetector {
    detector: <PERSON><PERSON><PERSON>Detector<f32>,
    sample_rate: u32,
    power_threshold: f32,
    clarity_threshold: f32,
    note_history: Vec<Option<Note>>,
    history_size: usize,
    buffer_size: usize, // 現在のバッファサイズを保存
}

impl NoteDetector {
    pub fn new(sample_rate: u32, buffer_size: usize) -> Self {
        Self {
            detector: McLeodDetector::new(buffer_size, buffer_size / 2),
            sample_rate,
            power_threshold: 0.01,
            clarity_threshold: 0.7,
            note_history: Vec::new(),
            history_size: 1, // 履歴サイズを小さくして、より素早く変化に対応（密度を上げる）
            buffer_size,
        }
    }

    // バッファサイズが変更された場合に検出器を再初期化するメソッド
    pub fn resize_detector(&mut self, buffer_size: usize) {
        self.detector = McLeodDetector::new(buffer_size, buffer_size / 2);
        self.buffer_size = buffer_size;
    }

    pub fn set_thresholds(&mut self, power: f32, clarity: f32) {
        self.power_threshold = power;
        self.clarity_threshold = clarity;
    }

    pub fn detect_pitch(&mut self, buffer: &[f32]) -> Option<(Note, f32)> {
        // バッファサイズが変更された場合は検出器を再初期化
        let buffer_size = buffer.len();

        if buffer_size != self.buffer_size {
            println!("バッファサイズが変更されました: {} -> {}", self.buffer_size, buffer_size);
            self.resize_detector(buffer_size);
        }

        // McLeodアルゴリズムでピッチを検出
        let pitch = self.detector.get_pitch(
            buffer,
            self.sample_rate as usize,
            self.power_threshold,
            self.clarity_threshold,
        );

        // ピッチが検出されたかどうかをデバッグ出力
        /*
        if let Some(p) = pitch.as_ref() {
            println!("ピッチ検出: 周波数 = {:.2} Hz, クラリティ = {:.2}", p.frequency, p.clarity);
        }
        */

        // 周波数からノートに変換
        let detected_note = pitch.as_ref().map(|p| self.frequency_to_note(p.frequency));
        self.note_history.push(detected_note);

        // 履歴サイズを制限
        if self.note_history.len() > self.history_size {
            self.note_history.remove(0);
        }

        // 履歴から最も頻度の高いノートを選択
        let most_frequent = self.get_most_frequent_note();

        // 検出されたピッチとノートを返す
        pitch.and_then(|p| {
            most_frequent.map(|note| (note, p.clarity))
        })
    }

    // 周波数から音階への変換
    fn frequency_to_note(&self, frequency: f32) -> Note {
        // A4 = 440Hz = 69
        let semitones_from_a4 = 12.0 * (frequency / 440.0).log2();
        let midi_note = 69.0 + semitones_from_a4;
        let midi_note_rounded = midi_note.round() as i32;

        // MIDIノート番号から音階名を計算
        let octave = (midi_note_rounded / 12) - 1;
        let note_in_octave = midi_note_rounded % 12;

        // 音階名とアクシデンタルを決定
        let (letter, accidental) = match note_in_octave {
            0 => ('C', None),
            1 => ('C', Some("#")),
            2 => ('D', None),
            3 => ('D', Some("#")),
            4 => ('E', None),
            5 => ('F', None),
            6 => ('F', Some("#")),
            7 => ('G', None),
            8 => ('G', Some("#")),
            9 => ('A', None),
            10 => ('A', Some("#")),
            11 => ('B', None),
            _ => unreachable!(),
        };

        Note {
            letter,
            accidental,
            octave: octave as u8,
        }
    }

    fn get_most_frequent_note(&self) -> Option<Note> {
        if self.note_history.is_empty() {
            return None;
        }

        // ノートの出現回数をカウント
        let mut note_counts: HashMap<Note, usize> = HashMap::new();
        for note_opt in &self.note_history {
            if let Some(note) = note_opt {
                *note_counts.entry(*note).or_insert(0) += 1;
            }
        }

        // 最も頻度の高いノートを見つける
        note_counts
            .into_iter()
            .max_by_key(|&(_, count)| count)
            .map(|(note, _)| note)
    }

    pub fn note_to_midi(&self, note: Note) -> u8 {
        // ノートをMIDIノート番号に変換
        // C4（中央ド）は60
        let octave = note.octave as i32;
        let note_value = match note.letter {
            'C' => 0,
            'D' => 2,
            'E' => 4,
            'F' => 5,
            'G' => 7,
            'A' => 9,
            'B' => 11,
            _ => 0,
        };

        // シャープ/フラットの調整
        let accidental_value = match note.accidental {
            Some("#") => 1,
            Some("b") => -1,
            _ => 0,
        };

        // MIDI番号を計算（C4 = 60）
        let midi_note = 60 + (octave - 4) * 12 + note_value + accidental_value;

        // 0-127の範囲に制限
        midi_note.max(0).min(127) as u8
    }

    pub fn frequency_to_midi_note(&self, frequency: f32) -> u8 {
        // 周波数からMIDIノート番号を計算
        // A4 = 440Hz = MIDI note 69
        if frequency <= 0.0 {
            return 0;
        }

        let note = 69.0 + 12.0 * (frequency / 440.0).log2();
        note.round().max(0.0).min(127.0) as u8
    }

    // 周波数からMIDIノート番号と、そのノートからのピッチベンド値を計算
    pub fn frequency_to_midi_with_pitch_bend(&self, frequency: f32) -> (u8, i16) {
        if frequency <= 0.0 {
            return (0, 0);
        }

        // 周波数からMIDIノート番号（浮動小数点）を計算
        let exact_note = 69.0 + 12.0 * (frequency / 440.0).log2();

        // 最も近いMIDIノート番号
        let midi_note = exact_note.round().max(0.0).min(127.0) as u8;

        // ノートからの差分（セント値）
        let cents = (exact_note - midi_note as f32) * 100.0;

        // セント値をピッチベンド値に変換（-8192〜8191）
        // 一般的に±2セミトーン（±200セント）の範囲を使用
        let pitch_bend = (cents / 200.0 * 8192.0) as i16;

        // 範囲を制限
        let pitch_bend = pitch_bend.max(-8192).min(8191);

        (midi_note, pitch_bend)
    }
}
