mod audio;
mod fft;
mod midi;
mod note_detection;
mod gui;

use anyhow::Result;
use std::io::{self, Read};
use std::sync::{mpsc, Arc, Mutex};
use std::thread;
use std::time::{Duration, Instant};

// 入力音量をMIDIチャンネル（0-15）に分割する関数
fn amplitude_to_channel(amplitude: f32, max_channels: u8) -> u8 {
    // 音量を0.0-1.0の範囲に正規化
    let normalized_amplitude = amplitude.min(1.0).max(0.0);

    // 正規化された音量をチャンネル数で分割
    let channel = (normalized_amplitude * max_channels as f32).floor() as u8;

    // チャンネルの範囲を制限（0-15）
    channel.min(max_channels - 1)
}

fn main() -> Result<()> {
    println!("リアルタイムオーディオ→MIDI変換プログラム");

    // アクティブなノートとスペクトルデータを保持する共有データ
    let active_notes = Arc::new(Mutex::new(gui::ActiveNotes::new()));
    let spectrum_data = Arc::new(Mutex::new(Vec::<f32>::new()));
    let complex_spectrum = Arc::new(Mutex::new(Vec::<(f32, f32)>::new())); // フーリエ円表現用の複素数スペクトル

    // オーディオキャプチャの初期化
    // println!("オーディオキャプチャの初期化を開始します...");
    let mut audio_capture = audio::AudioCapture::new();
    match audio_capture.initialize() {
        Ok(_) => {/* println!("オーディオキャプチャの初期化に成功しました") */},
        Err(e) => {
            // println!("オーディオキャプチャの初期化に失敗しました: {}", e);
            return Err(e);
        }
    }

    // サンプルレートの取得
    let sample_rate = audio_capture.get_sample_rate().unwrap_or(44100);
    // println!("サンプルレート: {}", sample_rate);

    // FFTサイズの設定
    // 実際のバッファサイズは動的に調整されるため、初期値として設定
    let fft_size = 2048; // 2のべき乗の値を使用
    // println!("FFTサイズを設定: {}", fft_size);
    let fft_processor = fft::FFTProcessor::new(fft_size);

    // MIDI出力の初期化
    let mut midi_output = midi::MidiOutputDevice::new(1); // ノート持続時間10ms（極限まで短くして密度を最大化）
    // println!("MIDIノートの持続時間: {}ミリ秒", 10);
    midi_output.initialize()?;

    // ノート検出器の初期化
    let mut note_detector = note_detection::NoteDetector::new(sample_rate, fft_size);
    note_detector.set_thresholds(0.001, 0.3); // パワーとクラリティのしきい値（非常に低い値で検出しやすくする）
    // println!("ピッチ検出のしきい値を設定: パワー={}, クラリティ={}", 0.001, 0.3);

    // チャンネル作成
    let (tx, rx) = mpsc::channel();

    // オーディオストリームの開始
    let _stream = audio_capture.start_stream(fft_size, tx)?;

    println!("処理を開始しました。Ctrl+Cで終了します。");
    println!("音を入力すると、検出された音階がMIDIに変換されます。");
    println!("音量調整: [+]キーで音量アップ、[-]キーで音量ダウン");
    println!("入力音量はMIDIチャンネル0-15に分割されます。");
    println!("周波数帯域の音量もMIDIチャンネル0-15に割り当てられます。");
    println!("ピッチベンド機能が有効です。検出された周波数に基づいて微調整されます。");

    // GUIウィンドウを起動するスレッド
    let active_notes_gui = Arc::clone(&active_notes);
    let spectrum_data_gui = Arc::clone(&spectrum_data);
    let complex_spectrum_gui = Arc::clone(&complex_spectrum);
    thread::spawn(move || {
        let mut app = gui::MidiKeyboardApp::new(active_notes_gui, spectrum_data_gui);
        // 複素数スペクトルデータを設定
        app.complex_spectrum = complex_spectrum_gui;
        app.run();
    });

    // キーボード入力を非ブロッキングモードで取得するためのスレッド
    let (key_tx, key_rx) = mpsc::channel();
    thread::spawn(move || {
        let mut stdin = io::stdin();
        let mut buffer = [0; 1];
        loop {
            if stdin.read_exact(&mut buffer).is_ok() {
                let key = buffer[0] as char;
                if key_tx.send(key).is_err() {
                    break;
                }
            }
            thread::sleep(Duration::from_millis(10));
        }
    });

    // 最後に検出されたノートとチャンネル、その時刻
    let mut last_note: Option<u8> = None;
    let mut last_channel: Option<u8> = None; // 将来の拡張のために保持
    let mut last_note_time = Instant::now();
    let note_change_threshold = Duration::from_nanos(200); // ノート変更の最小間隔（極限まで短くして密度を最大化）
    // println!("ノート変更の最小間隔: {}ミリ秒", 1);

    // 音量調整のための変数
    let mut volume_gain = 1.0; // 音量ゲイン（1.0 = 通常、2.0 = 2倍の音量）
    let mut last_volume_check = Instant::now();
    let volume_check_interval = Duration::from_millis(1); // 音量チェックの間隔
    let mut max_amplitude = 0.0f32; // 直近の最大振幅

    // メインループ
    loop {
        // オーディオバッファを受信
        if let Ok(buffer) = rx.recv() {
            // println!("メインループ: オーディオバッファ受信 - サイズ: {}", buffer.len());

            // 入力音量を計算
            let current_max_amplitude = buffer.iter().map(|&x| x.abs()).fold(0.0f32, f32::max);
            max_amplitude = max_amplitude.max(current_max_amplitude);

            // 定期的に音量ゲインを調整
            let now = Instant::now();
            if now.duration_since(last_volume_check) > volume_check_interval {
                // 音量が小さすぎる場合はゲインを上げる
                if max_amplitude < 0.1 {
                    volume_gain = f32::min(volume_gain * 1.2, 10.0); // 最大10倍まで
                }
                // 音量が大きすぎる場合はゲインを下げる
                else if max_amplitude > 0.8 {
                    volume_gain = f32::max(volume_gain * 0.8, 0.1); // 最小0.1倍まで
                }

                // リセット
                max_amplitude = 0.0;
                last_volume_check = now;
            }
            // FFT処理
            let fft_result = fft_processor.process(&buffer);
            /*
            match &fft_result {
                Ok(spectrum) => println!("FFT処理成功: スペクトルサイズ: {}", spectrum.len()),
                Err(e) => println!("FFT処理エラー: {}", e),
            }
            */
            if let Ok(spectrum) = fft_result {
                // スペクトルデータをGUIと共有
                {
                    let mut spectrum_data_lock = spectrum_data.lock().unwrap();
                    *spectrum_data_lock = spectrum.clone();
                }

                // 複素数FFT処理（フーリエ円表現用）
                if let Ok(complex_result) = fft_processor.process_complex(&buffer) {
                    // フーリエ円表現用のデータを計算（最大20個の円）
                    let circles = fft_processor.get_fourier_circles(&complex_result, 20);

                    // 複素数スペクトルデータをGUIと共有
                    let mut complex_spectrum_lock = complex_spectrum.lock().unwrap();
                    *complex_spectrum_lock = circles;
                }

                // 周波数帯域の音量を計算（16帯域 = MIDIチャンネル0-15）
                let bands = fft_processor.get_frequency_bands(&spectrum, sample_rate, 16);

                // 各周波数帯域の音量をMIDIチャンネルに割り当てる
                // 各チャンネルで同じノート（C4 = 60）を使用し、音量だけを変える
                let midi_note = 60; // C4

                for (channel, &amplitude) in bands.iter().enumerate() {
                    // 振幅からベロシティに変換（音量ゲインを適用）
                    let velocity = ((amplitude * volume_gain * 127.0 * 10.0) as u8).min(127); // 10倍に増幅して見やすく

                    // 音量が一定以上の場合のみノートを送信（ノイズ除去）
                    if velocity > 5 {
                        // チャンネルごとに異なるノートを使用（低音から高音へ）
                        let channel_note = midi_note + (channel as u8 % 16);
                        midi_output.note_on_channel(channel_note, velocity, channel as u8)?;

                        // アクティブノートリストに追加
                        active_notes.lock().unwrap().add_note(channel_note, velocity, channel as u8);
                    }
                }

                // 入力音量をMIDIチャンネルに分割
                // 現在のバッファの音量を計算
                let buffer_amplitude = buffer.iter().map(|&x| x.abs()).fold(0.0f32, f32::max);

                // 音量ゲインを適用
                let adjusted_amplitude = (buffer_amplitude * volume_gain).min(1.0);

                // 音量に基づいてチャンネルを決定
                let channel = amplitude_to_channel(adjusted_amplitude, 16);

                // このチャンネルに対応するノートを送信
                let base_note = 48; // C3（周波数帯域のC4と区別するため1オクターブ下げる）
                let channel_note = base_note + channel;

                // 音量からベロシティを計算
                let velocity = (adjusted_amplitude * 127.0) as u8;

                // ノートオン（対応するチャンネルを使用）
                if velocity > 5 { // ノイズ除去のため小さすぎる音は無視
                    midi_output.note_on_channel(channel_note, velocity, channel)?;

                    // アクティブノートリストに追加
                    active_notes.lock().unwrap().add_note(channel_note, velocity, channel);
                }

                // FFTスペクトルから直接複数のノートを検出
                // 周波数ピークを検出（最大20個、非常に低いしきい値で）
                let peaks = fft_processor.get_dominant_frequencies(&spectrum, sample_rate, 0.0001, 20);
                let now = Instant::now();

                // 時間間隔をチェック
                if now.duration_since(last_note_time) > note_change_threshold {
                    // アクティブノートを更新（自動的にノートオフが送信される）
                    midi_output.update()?;

                    // アクティブノートリストをクリア
                    active_notes.lock().unwrap().clear();

                    // 検出されたすべてのピークに対してMIDIノートを送信（最大10個）
                    for (i, &(frequency, amplitude)) in peaks.iter().enumerate().take(10) {
                        // 周波数からMIDIノートとピッチベンド値を計算
                        let (midi_note, pitch_bend) = note_detector.frequency_to_midi_with_pitch_bend(frequency);

                        // 振幅からベロシティに変換（大きな音ほど大きなベロシティ）
                        // 音量ゲインを適用
                        let velocity = ((amplitude * volume_gain * 127.0) as u8).min(127);

                        // 同時に複数のノートを送信するため、少しずらす
                        if i > 0 {
                            thread::sleep(Duration::from_micros(100));
                        }

                        // 音量に基づいてチャンネルを決定
                        let channel = amplitude_to_channel(amplitude * volume_gain, 16);

                        // ピッチベンドを送信
                        midi_output.pitch_bend_channel(pitch_bend, channel)?;

                        // ノートオン（音量に対応するチャンネルを使用）
                        midi_output.note_on_channel(midi_note, velocity, channel)?;

                        // アクティブノートリストに追加
                        let mut active_notes_lock = active_notes.lock().unwrap();
                        active_notes_lock.add_note(midi_note, velocity, channel);
                        active_notes_lock.set_pitch_bend(channel, pitch_bend);
                    }

                    // 最後のノートとチャンネルを記録（最も強いピークのノート）
                    if let Some(&(frequency, amplitude)) = peaks.first() {
                        last_note = Some(note_detector.frequency_to_midi_note(frequency));
                        last_channel = Some(amplitude_to_channel(amplitude * volume_gain, 16));
                        last_note_time = now;
                    }
                }

                // 従来のピッチ検出も併用
                if let Some((detected_note, clarity)) = note_detector.detect_pitch(&buffer) {
                    let midi_note = note_detector.note_to_midi(detected_note);
                    let now = Instant::now();

                    // ノートの変更を処理
                    if last_note != Some(midi_note) && now.duration_since(last_note_time) > note_change_threshold {
                        // 新しいノートをオンにする
                        // 音量ゲインを適用
                        let velocity = (clarity * volume_gain * 127.0).min(127.0) as u8;

                        // 音量（クラリティ）に基づいてチャンネルを決定
                        let channel = amplitude_to_channel(clarity * volume_gain, 16);

                        // ピッチベンド値を計算（ここでは検出されたノートの周波数から計算）
                        // 検出されたノートの周波数を取得（近似値）
                        let _note_frequency = 440.0 * f32::powf(2.0, (midi_note as f32 - 69.0) / 12.0);

                        // 実際の周波数との差からピッチベンド値を計算
                        // ここでは簡易的に0を使用（正確な周波数が取得できないため）
                        let pitch_bend: i16 = 0;

                        // ピッチベンドを送信
                        midi_output.pitch_bend_channel(pitch_bend, channel)?;

                        // ノートオン（音量に対応するチャンネルを使用）
                        midi_output.note_on_channel(midi_note, velocity, channel)?;

                        // アクティブノートリストに追加
                        let mut active_notes_lock = active_notes.lock().unwrap();
                        active_notes_lock.add_note(midi_note, velocity, channel);
                        active_notes_lock.set_pitch_bend(channel, pitch_bend);

                        last_note = Some(midi_note);
                        last_channel = Some(channel);
                        last_note_time = now;
                    }
                }
            }
        }

        // キーボード入力を処理
        if let Ok(key) = key_rx.try_recv() {
            match key {
                '+' => {
                    // 音量を上げる
                    volume_gain = f32::min(volume_gain * 1.5, 10.0);
                    println!("音量アップ: ゲイン = {:.1}", volume_gain);
                },
                '-' => {
                    // 音量を下げる
                    volume_gain = f32::max(volume_gain * 0.7, 0.1);
                    println!("音量ダウン: ゲイン = {:.1}", volume_gain);
                },
                _ => {}
            }
        }

        // 最小限の待機（ほぼ待機なし）
        thread::sleep(Duration::from_micros(1)); // 1マイクロ秒に短縮（実質的に待機なし）
    }
}
