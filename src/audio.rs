use anyhow::{anyhow, Result};
use cpal::traits::{DeviceTrait, HostTrait, StreamTrait};
use cpal::SampleFormat;
use std::sync::mpsc;
use std::sync::{Arc, Mutex};

pub struct AudioCapture {
    host: cpal::Host,
    device: Option<cpal::Device>,
    config: Option<cpal::SupportedStreamConfig>,
    sample_rate: Option<u32>,
}

impl AudioCapture {
    pub fn new() -> Self {
        let host = cpal::default_host();
        Self {
            host,
            device: None,
            config: None,
            sample_rate: None,
        }
    }

    pub fn initialize(&mut self) -> Result<()> {
        // 利用可能な入力デバイスを表示
        println!("利用可能なオーディオ入力デバイス:");
        let input_devices = self.host.input_devices()?;
        let devices: Vec<_> = input_devices.collect();

        if devices.is_empty() {
            return Err(anyhow!("入力デバイスが見つかりません"));
        }

        for (i, device) in devices.iter().enumerate() {
            println!("{}: {}", i, device.name()?);
        }

        // デバイスの選択
        println!("使用するオーディオ入力デバイスの番号を入力してください:");
        println!("（マイクやオーディオ入力デバイスを選択してください。音が出るスピーカーではなく、音を入力するデバイスです）");
        let mut input = String::new();
        std::io::stdin().read_line(&mut input)?;

        let device_index = input.trim().parse::<usize>().unwrap_or(0);
        let device_index = if device_index >= devices.len() { 0 } else { device_index };

        let device = &devices[device_index];
        println!("選択されたデバイス: {}", device.name()?);

        // サポートされている設定を取得
        let config = device
            .default_input_config()
            .map_err(|e| anyhow!("デフォルト入力設定の取得に失敗: {}", e))?;

        println!("入力設定: {:?}", config);
        self.sample_rate = Some(config.sample_rate().0);
        self.device = Some(device.clone());
        self.config = Some(config);

        Ok(())
    }

    pub fn start_stream(
        &self,
        buffer_size: usize,
        tx: mpsc::Sender<Vec<f32>>,
    ) -> Result<cpal::Stream> {
        let device = self
            .device
            .as_ref()
            .ok_or_else(|| anyhow!("デバイスが初期化されていません"))?;
        let config = self
            .config
            .as_ref()
            .ok_or_else(|| anyhow!("設定が初期化されていません"))?;

        let err_fn = |err| eprintln!("オーディオストリームでエラーが発生: {}", err);

        let buffer = Arc::new(Mutex::new(Vec::with_capacity(buffer_size)));
        let buffer_clone = Arc::clone(&buffer);

        let stream = match config.sample_format() {
            SampleFormat::F32 => self.build_input_stream_f32(device, config, buffer_clone, tx, err_fn)?,
            SampleFormat::I16 => self.build_input_stream_i16(device, config, buffer_clone, tx, err_fn)?,
            SampleFormat::U16 => self.build_input_stream_u16(device, config, buffer_clone, tx, err_fn)?,
            _ => return Err(anyhow!("サポートされていないサンプル形式です")),
        };

        stream.play()?;
        Ok(stream)
    }

    fn build_input_stream_f32(
        &self,
        device: &cpal::Device,
        config: &cpal::SupportedStreamConfig,
        buffer: Arc<Mutex<Vec<f32>>>,
        tx: mpsc::Sender<Vec<f32>>,
        err_fn: impl FnMut(cpal::StreamError) + Send + 'static + Clone,
    ) -> Result<cpal::Stream> {
        let channels = config.channels() as usize;
        let buffer_size = buffer.lock().unwrap().capacity();

        let stream = device.build_input_stream(
            &config.config(),
            move |data: &[f32], _: &cpal::InputCallbackInfo| {
                let mut buffer = buffer.lock().unwrap();

                // サンプルをf32に変換して追加
                for frame in data.chunks(channels) {
                    // モノラルに変換（複数チャンネルの平均を取る）
                    let sum: f32 = frame.iter().sum();
                    let sample = sum / channels as f32;

                    // ゲインを上げる（入力が小さすぎるため）
                    let amplified_sample = sample * 100.0; // 100倍に増幅

                    buffer.push(amplified_sample);
                }

                // バッファが一杯になったら処理
                if buffer.len() >= buffer_size {
                    let data_to_send = buffer.clone();
                    buffer.clear();

                    // オーディオデータが取得されていることを確認するためのデバッグ出力
                    /*
                    println!("F32オーディオデータ取得: {} サンプル, 最大振幅: {:.6}",
                             data_to_send.len(),
                             data_to_send.iter().map(|&x| x.abs()).fold(0.0f32, f32::max));
                    */

                    // 処理スレッドにデータを送信
                    if tx.send(data_to_send).is_err() {
                        eprintln!("データの送信に失敗しました");
                    }
                }
            },
            err_fn,
            None,
        )?;

        Ok(stream)
    }

    fn build_input_stream_i16(
        &self,
        device: &cpal::Device,
        config: &cpal::SupportedStreamConfig,
        buffer: Arc<Mutex<Vec<f32>>>,
        tx: mpsc::Sender<Vec<f32>>,
        err_fn: impl FnMut(cpal::StreamError) + Send + 'static + Clone,
    ) -> Result<cpal::Stream> {
        let channels = config.channels() as usize;
        let buffer_size = buffer.lock().unwrap().capacity();

        let stream = device.build_input_stream(
            &config.config(),
            move |data: &[i16], _: &cpal::InputCallbackInfo| {
                let mut buffer = buffer.lock().unwrap();

                // サンプルをf32に変換して追加
                for frame in data.chunks(channels) {
                    // モノラルに変換（複数チャンネルの平均を取る）
                    let sum: i32 = frame.iter().map(|&s| s as i32).sum();
                    let sample = (sum as f32 / channels as f32) / 32768.0;

                    // ゲインを上げる（入力が小さすぎるため）
                    let amplified_sample = sample * 100.0; // 100倍に増幅

                    buffer.push(amplified_sample);
                }

                // バッファが一杯になったら処理
                if buffer.len() >= buffer_size {
                    let data_to_send = buffer.clone();
                    buffer.clear();

                    // 処理スレッドにデータを送信
                    if tx.send(data_to_send).is_err() {
                        eprintln!("データの送信に失敗しました");
                    }
                }
            },
            err_fn,
            None,
        )?;

        Ok(stream)
    }

    fn build_input_stream_u16(
        &self,
        device: &cpal::Device,
        config: &cpal::SupportedStreamConfig,
        buffer: Arc<Mutex<Vec<f32>>>,
        tx: mpsc::Sender<Vec<f32>>,
        err_fn: impl FnMut(cpal::StreamError) + Send + 'static + Clone,
    ) -> Result<cpal::Stream> {
        let channels = config.channels() as usize;
        let buffer_size = buffer.lock().unwrap().capacity();

        let stream = device.build_input_stream(
            &config.config(),
            move |data: &[u16], _: &cpal::InputCallbackInfo| {
                let mut buffer = buffer.lock().unwrap();

                // サンプルをf32に変換して追加
                for frame in data.chunks(channels) {
                    // モノラルに変換（複数チャンネルの平均を取る）
                    let sum: u32 = frame.iter().map(|&s| s as u32).sum();
                    // u16の中点（32768）を0とする
                    let sample = ((sum as f32 / channels as f32) - 32768.0) / 32768.0;

                    // ゲインを上げる（入力が小さすぎるため）
                    let amplified_sample = sample * 100.0; // 100倍に増幅

                    buffer.push(amplified_sample);
                }

                // バッファが一杯になったら処理
                if buffer.len() >= buffer_size {
                    let data_to_send = buffer.clone();
                    buffer.clear();

                    // 処理スレッドにデータを送信
                    if tx.send(data_to_send).is_err() {
                        eprintln!("データの送信に失敗しました");
                    }
                }
            },
            err_fn,
            None,
        )?;

        Ok(stream)
    }

    pub fn get_sample_rate(&self) -> Option<u32> {
        self.sample_rate
    }
}
