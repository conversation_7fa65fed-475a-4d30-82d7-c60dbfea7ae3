use raylib::prelude::*;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::f32::consts::PI;

// Display constants
const WIDTH: i32 = 1500;
const HEIGHT: i32 = 600;
const SPECTRUM_HEIGHT: i32 = 250;
const KEYBOARD_Y_POSITION: i32 = 350;
const HISTORY_SIZE: usize = 40; // Number of history frames to keep

// フーリエ円表示用の定数
const FOURIER_CIRCLE_CENTER_X: i32 = 300;
const FOURIER_CIRCLE_CENTER_Y: i32 = 150;
const MAX_CIRCLE_RADIUS: f32 = 120.0; // 最大円の半径
const MAX_CIRCLES: usize = 30; // 表示する円の最大数

// Piano keyboard constants
const WHITE_KEY_WIDTH: i32 = 20;
const WHITE_KEY_HEIGHT: i32 = 120;
const BLACK_KEY_WIDTH: i32 = 14;
const BLACK_KEY_HEIGHT: i32 = 80;
const KEYBOARD_START_NOTE: u8 = 0;
const KEYBOARD_END_NOTE: u8 = 127;

// White key layout definition
const WHITE_KEYS: [bool; 12] = [true, false, true, false, true, true, false, true, false, true, false, true];

// Structure to hold active notes
pub struct ActiveNotes {
    pub notes: HashMap<u8, (u8, u8)>, // note number -> (velocity, channel)
    pub pitch_bends: HashMap<u8, i16>, // channel -> pitch bend value (-8192 to 8191)
}

impl ActiveNotes {
    pub fn new() -> Self {
        Self {
            notes: HashMap::new(),
            pitch_bends: HashMap::new(),
        }
    }

    pub fn add_note(&mut self, note: u8, velocity: u8, channel: u8) {
        self.notes.insert(note, (velocity, channel));
    }

    pub fn remove_note(&mut self, note: u8) {
        self.notes.remove(&note);
    }

    pub fn set_pitch_bend(&mut self, channel: u8, value: i16) {
        self.pitch_bends.insert(channel, value);
    }

    pub fn get_pitch_bend(&self, channel: u8) -> i16 {
        *self.pitch_bends.get(&channel).unwrap_or(&0)
    }

    pub fn clear(&mut self) {
        self.notes.clear();
        // ピッチベンドはクリアしない（ノートがオフになってもピッチベンドは維持）
    }
}

// フーリエ円表現用の構造体
struct FourierCircle {
    radius: f32,     // 円の半径
    phase: f32,      // 初期位相
    frequency: f32,  // 周波数（回転速度）
    color: Color,    // 円の色
}

// GUI Application
pub struct MidiKeyboardApp {
    active_notes: Arc<Mutex<ActiveNotes>>,
    spectrum_data: Arc<Mutex<Vec<f32>>>,
    spectrum_history: Vec<Vec<f32>>,
    midi_notes_history: Vec<HashMap<u8, (u8, u8)>>, // 履歴: MIDIノート -> (ベロシティ, チャンネル)
    fourier_circles: Vec<FourierCircle>, // フーリエ級数の円表現
    pub complex_spectrum: Arc<Mutex<Vec<(f32, f32)>>>, // 複素数FFT結果（半径、位相）
    animation_time: f32, // アニメーション用の時間変数
    rl: RaylibHandle,
    thread: RaylibThread,
}

impl MidiKeyboardApp {
    pub fn new(active_notes: Arc<Mutex<ActiveNotes>>, spectrum_data: Arc<Mutex<Vec<f32>>>) -> Self {
        let (mut rl, thread) = raylib::init()
            .size(WIDTH, HEIGHT)
            .title("MIDI Keyboard Display")
            .build();

        // Set frame rate
        rl.set_target_fps(60);

        Self {
            active_notes,
            spectrum_data,
            spectrum_history: Vec::new(),
            midi_notes_history: Vec::new(),
            fourier_circles: Vec::new(),
            complex_spectrum: Arc::new(Mutex::new(Vec::new())),
            animation_time: 0.0,
            rl,
            thread,
        }
    }

    // Run main loop
    pub fn run(&mut self) {
        while !self.rl.window_should_close() {
            let mut d = self.rl.begin_drawing(&self.thread);

            d.clear_background(Color::WHITE);

            // Copy data to release the borrow
            let active_notes_copy = {
                let active_notes = self.active_notes.lock().unwrap();
                let notes_copy = active_notes.notes.clone();

                // アクティブノートを履歴に追加（スペクトラムと同様に最新のデータを先頭に）
                if !notes_copy.is_empty() {
                    self.midi_notes_history.insert(0, notes_copy.clone());

                    // 履歴サイズを制限
                    if self.midi_notes_history.len() > HISTORY_SIZE {
                        self.midi_notes_history.pop();
                    }
                }

                notes_copy
            };

            let spectrum_data_copy = {
                let spectrum_data = self.spectrum_data.lock().unwrap();
                let data_copy = spectrum_data.clone();

                // Add current spectrum to history at the beginning
                if !data_copy.is_empty() {
                    // Insert at the beginning (newest data first)
                    self.spectrum_history.insert(0, data_copy.clone());

                    // Keep only the most recent frames
                    if self.spectrum_history.len() > HISTORY_SIZE {
                        self.spectrum_history.pop(); // Remove oldest frame
                    }
                }

                data_copy
            };

            // Draw FFT spectrum at the top
            // Clone history to avoid borrowing issues
            let history_copy = self.spectrum_history.clone();
            let midi_history_copy = self.midi_notes_history.clone();

            // スペクトラムを描画
            Self::draw_spectrum(&mut d, &spectrum_data_copy, &history_copy);

            // スペクトラムの手前にMIDIノートの履歴を描画
            Self::draw_midi_notes_history(&mut d, &midi_history_copy);

            // フーリエ円表現を描画
            // アニメーション時間を更新
            self.animation_time += 0.02;

            // 複素数スペクトルデータを取得
            let complex_data = self.complex_spectrum.lock().unwrap().clone();

            // フーリエ円表現を描画
            Self::draw_fourier_circles(&mut d, &complex_data, self.animation_time);

            // Draw keyboard at the bottom
            d.draw_rectangle(0, KEYBOARD_Y_POSITION - 10, WIDTH, HEIGHT - KEYBOARD_Y_POSITION + 10, Color::WHITE);
            d.draw_line(0, KEYBOARD_Y_POSITION - 10, WIDTH, KEYBOARD_Y_POSITION - 10, Color::BLACK);

            // Draw keyboard with Y position offset
            MidiKeyboardApp::draw_keyboard(&mut d, &active_notes_copy, KEYBOARD_Y_POSITION);

            // Add description
            d.draw_text("Colors represent MIDI channels (0-15)", 10, KEYBOARD_Y_POSITION + WHITE_KEY_HEIGHT + 20, 20, Color::BLACK);
            d.draw_text("Higher volume corresponds to higher channel numbers", 10, KEYBOARD_Y_POSITION + WHITE_KEY_HEIGHT + 50, 20, Color::BLACK);

            // Draw color legend
            MidiKeyboardApp::draw_color_legend(&mut d, KEYBOARD_Y_POSITION + WHITE_KEY_HEIGHT + 80);
        }
    }

    // フーリエ円表現を描画するメソッド（静的メソッド）
    fn draw_fourier_circles(d: &mut RaylibDrawHandle, complex_data: &Vec<(f32, f32)>, animation_time: f32) {
        if complex_data.is_empty() {
            return;
        }

        // 円の中心座標
        let center_x = FOURIER_CIRCLE_CENTER_X;
        let center_y = FOURIER_CIRCLE_CENTER_Y;

        // アニメーション時間は外部から渡される

        // 円の軌跡を描画するための点の配列
        let mut points = Vec::new();
        let mut current_x = center_x as f32;
        let mut current_y = center_y as f32;

        // 各円を描画
        for (i, &(radius, phase)) in complex_data.iter().enumerate().take(MAX_CIRCLES) {
            // 正規化された半径（最大半径に対する比率）
            let normalized_radius = radius * MAX_CIRCLE_RADIUS / complex_data[0].0;

            // 周波数（円の回転速度）- インデックスに比例
            let frequency = (i + 1) as f32;

            // 現在の角度を計算（時間 * 周波数 + 初期位相）
            let angle = animation_time * frequency + phase;

            // 円の中心座標を計算
            let circle_center_x = current_x;
            let circle_center_y = current_y;

            // 円の縁の座標を計算
            let edge_x = circle_center_x + normalized_radius * angle.cos();
            let edge_y = circle_center_y + normalized_radius * angle.sin();

            // 円を描画（塗りつぶし）
            // 円の大きさに応じてグラデーション（大きい円ほど青、小さい円ほど赤）
            let size_ratio = normalized_radius / MAX_CIRCLE_RADIUS;
            let circle_color = Color::new(
                (255.0 * (1.0 - size_ratio)).min(255.0) as u8,  // R: 小さい円ほど赤くなる
                ((i as f32 / MAX_CIRCLES as f32) * 100.0) as u8, // G: 周波数が高いほど緑が増える
                (255.0 * size_ratio).min(255.0) as u8,          // B: 大きい円ほど青くなる
                50  // 透明度を低めに設定して重なりを見やすく
            );

            // 塗りつぶした円を描画
            d.draw_circle(
                circle_center_x as i32,
                circle_center_y as i32,
                normalized_radius,
                circle_color
            );

            // 円の輪郭も描画して見やすくする
            d.draw_circle_lines(
                circle_center_x as i32,
                circle_center_y as i32,
                normalized_radius,
                Color::WHITE
            );

            // 中心から縁への線を描画
            d.draw_line(
                circle_center_x as i32,
                circle_center_y as i32,
                edge_x as i32,
                edge_y as i32,
                Color::BLUE
            );

            // 次の円の中心を更新
            current_x = edge_x;
            current_y = edge_y;

            // 軌跡の点を追加
            points.push((current_x as i32, current_y as i32));
        }

        // 軌跡を描画（太く、グラデーションで）
        if !points.is_empty() {
            // 軌跡の点を結ぶ線を描画（太めの線で）
            for i in 1..points.len() {
                // 軌跡の位置に応じてグラデーション（始点は青、終点は赤）
                let progress = i as f32 / points.len() as f32;
                let line_color = Color::new(
                    (255.0 * progress) as u8,      // R: 終点に近いほど赤くなる
                    0,                             // G: 緑は使わない
                    (255.0 * (1.0 - progress)) as u8, // B: 始点に近いほど青くなる
                    200                            // 不透明度を高めに
                );

                // 太い線を描画するために複数の線を少しずらして描画
                for offset in -1..=1 {
                    d.draw_line(
                        points[i-1].0 + offset,
                        points[i-1].1 + offset,
                        points[i].0 + offset,
                        points[i].1 + offset,
                        line_color
                    );
                }
            }

            // 軌跡の各点に小さな円を描画
            for (i, &point) in points.iter().enumerate() {
                let progress = i as f32 / points.len() as f32;
                let dot_color = Color::new(
                    (255.0 * progress) as u8,
                    (100.0 * progress) as u8,
                    (255.0 * (1.0 - progress)) as u8,
                    200
                );
                d.draw_circle(point.0, point.1, 2.0, dot_color);
            }

            // 軌跡の最終点に大きめの円を描画
            let last_point = points.last().unwrap();
            d.draw_circle(last_point.0, last_point.1, 8.0, Color::RED);
            d.draw_circle_lines(last_point.0, last_point.1, 8.0, Color::WHITE);
        }

        // 説明テキスト（日本語）
        d.draw_text(
            "フーリエ変換の円表現",
            center_x - 150,
            center_y - 180,
            20,
            Color::WHITE
        );
        d.draw_text(
            "各円は周波数成分を表しています",
            center_x - 150,
            center_y - 150,
            16,
            Color::WHITE
        );
        d.draw_text(
            "大きい円ほど低周波、小さい円ほど高周波",
            center_x - 150,
            center_y - 130,
            16,
            Color::WHITE
        );
    }

    // MIDIノートの履歴を描画するメソッド
    fn draw_midi_notes_history(d: &mut RaylibDrawHandle, midi_history: &Vec<HashMap<u8, (u8, u8)>>) {
        if midi_history.is_empty() {
            return;
        }

        // 履歴の各フレームを描画
        let total_frames = midi_history.len();

        for (i, notes) in midi_history.iter().enumerate() {
            // 透明度を計算（新しいフレームほど不透明に）
            let alpha = (255.0 - (i as f32 / total_frames as f32 * 200.0)) as u8;

            // Y位置を計算（新しいフレームほど下に）
            // スペクトラムの高さ内で表示
            let row_height = 6; // スペクトラムフレームと同じ高さ
            let y_offset = SPECTRUM_HEIGHT - row_height - i as i32 * row_height;

            // 各ノートを描画
            for (&note, &(_velocity, channel)) in notes {
                // 白鍵のインデックスを計算
                let mut white_key_index = 0;
                for n in KEYBOARD_START_NOTE..=note {
                    if is_white_key(n) {
                        white_key_index += 1;
                    }
                }

                // 黒鍵か白鍵かで位置を調整
                let x = if is_white_key(note) {
                    // 白鍵の場合
                    (white_key_index - 1) * WHITE_KEY_WIDTH
                } else {
                    // 黒鍵の場合
                    let mut prev_white_key = 0;
                    for n in KEYBOARD_START_NOTE..=note {
                        if is_white_key(n) {
                            prev_white_key += 1;
                        }
                    }
                    (prev_white_key - 1) * WHITE_KEY_WIDTH + (WHITE_KEY_WIDTH * 7 / 10)
                };

                // キーの幅を取得
                let key_width = if is_white_key(note) { WHITE_KEY_WIDTH } else { BLACK_KEY_WIDTH };

                // チャンネルに基づいて色を取得
                let mut color = get_channel_color(channel);
                color.a = alpha; // 透明度を設定

                // ノートを描画（小さな長方形）
                d.draw_rectangle(x, y_offset, key_width, row_height, color);
            }
        }
    }

    // Draw FFT spectrum visualization with history (static method)
    fn draw_spectrum(d: &mut RaylibDrawHandle, spectrum_data: &Vec<f32>, history: &Vec<Vec<f32>>) {

        if spectrum_data.is_empty() && history.is_empty() {
            // Draw empty spectrum area with border
            d.draw_rectangle(0, 0, WIDTH, SPECTRUM_HEIGHT, Color::BLACK);
            d.draw_text("Waiting for audio input...", WIDTH / 2 - 100, SPECTRUM_HEIGHT / 2, 20, Color::WHITE);
            return;
        }

        // Draw spectrum background
        d.draw_rectangle(0, 0, WIDTH, SPECTRUM_HEIGHT, Color::BLACK);

        // Draw piano key grid lines
        let mut white_key_index = 0;
        for note in KEYBOARD_START_NOTE..=KEYBOARD_END_NOTE {
            if is_white_key(note) {
                let x = white_key_index * WHITE_KEY_WIDTH;

                // Draw vertical grid line
                let grid_color = if note % 12 == 0 {
                    // C notes get a brighter line
                    Color::new(80, 80, 80, 255)
                } else {
                    Color::new(40, 40, 40, 255)
                };

                d.draw_line(x, 0, x, SPECTRUM_HEIGHT, grid_color);

                // Draw note labels at the bottom
                if note % 12 == 0 {
                    d.draw_text(&format!("C{}", note / 12), x + 2, SPECTRUM_HEIGHT - 15, 10, Color::WHITE);
                }

                white_key_index += 1;
            }
        }

        // Draw horizontal lines for reference
        for i in 1..5 {
            let y = SPECTRUM_HEIGHT - (SPECTRUM_HEIGHT * i / 5);
            d.draw_line(0, y, WIDTH, y, Color::new(40, 40, 40, 255));
        }

        // Draw all frames (history already has newest frames first)
        let total_frames = history.len();

        // Draw all frames from history (newest frames are already at index 0)
        for (i, frame) in history.iter().enumerate() {
            // Skip empty frames
            if frame.is_empty() {
                continue;
            }

            // Calculate alpha based on age (newer = more opaque)
            let alpha = (255.0 - (i as f32 / total_frames as f32 * 200.0)) as u8;

            // Draw this frame
            Self::draw_spectrum_frame(d, frame, i, alpha);
        }

        // If we have current data that's not yet in history, draw it at the bottom
        if !spectrum_data.is_empty() && (history.is_empty() || spectrum_data != &history[0]) {
            Self::draw_spectrum_frame(d, spectrum_data, 0, 255);
        }

        // Draw spectrum title and explanation
        d.draw_text("FFT Frequency Spectrum (Piano-aligned)", 10, 10, 20, Color::WHITE);
        d.draw_text("Newer data flows from bottom to top", WIDTH - 300, 10, 16, Color::WHITE);
        d.draw_text("MIDI notes are shown in front of spectrum", WIDTH - 300, 30, 16, Color::WHITE);
    }

    // Helper method to draw a single spectrum frame
    fn draw_spectrum_frame(d: &mut RaylibDrawHandle, spectrum: &Vec<f32>, frame_idx: usize, alpha: u8) {
        // Calculate row height based on history size
        let row_height = 6; // Slightly taller rows for better visibility

        // Calculate y position - newest frame (index 0) at the bottom
        // Higher indices (older frames) move up the display
        let y_offset = SPECTRUM_HEIGHT - row_height - frame_idx as i32 * row_height;

        // Count total white keys for width calculation
        let mut total_white_keys = 0;
        for note in KEYBOARD_START_NOTE..=KEYBOARD_END_NOTE {
            if is_white_key(note) {
                total_white_keys += 1;
            }
        }

        // Create a mapping from white key index to MIDI note
        let mut white_key_to_note = Vec::new();
        for note in KEYBOARD_START_NOTE..=KEYBOARD_END_NOTE {
            if is_white_key(note) {
                white_key_to_note.push(note);
            }
        }

        // Draw spectrum data aligned with piano keys
        for white_key_index in 0..total_white_keys {
            // Get the MIDI note for this white key
            let note = white_key_to_note[white_key_index];

            // Calculate x position - this aligns with the keyboard below
            let x = white_key_index as i32 * WHITE_KEY_WIDTH;

            // Find the corresponding frequency bin
            // MIDI note to frequency: f = 440 * 2^((n-69)/12)
            let frequency = 440.0 * f32::powf(2.0, (note as f32 - 69.0) / 12.0);

            // Map frequency to spectrum bin index (approximate)
            // Spectrum data is arranged from low to high frequency (0 to N-1)
            // For a 44.1kHz sample rate, Nyquist frequency is 22.05kHz
            let bin_index = (frequency / 22050.0 * spectrum.len() as f32) as usize;
            let bin_index = bin_index.min(spectrum.len() - 1);

            // Get amplitude for this note
            let amplitude = if bin_index < spectrum.len() {
                spectrum[bin_index].min(1.0)
            } else {
                0.0
            };

            // Calculate color based on amplitude (blue to red)
            let r = (amplitude * 255.0) as u8;
            let b = 255 - r;
            let color = Color::new(r, 0, b, alpha);

            // Draw rectangle for this note
            d.draw_rectangle(x, y_offset, WHITE_KEY_WIDTH, row_height, color);
        }
    }

    // Draw color legend for MIDI channels
    fn draw_color_legend(d: &mut RaylibDrawHandle, y_position: i32) {
        // Legend start position
        let x_start = 10;

        // Draw legend title
        d.draw_text("MIDI Channel Colors (0-15):", x_start, y_position, 20, Color::BLACK);

        // Draw color boxes for each channel
        let box_width = 40;
        let box_height = 30;
        let y_boxes = y_position + 25;

        for channel in 0..16 {
            let x = x_start + channel as i32 * (box_width + 5);
            let color = get_channel_color(channel);

            // Draw colored box
            d.draw_rectangle(x, y_boxes, box_width, box_height, color);
            d.draw_rectangle_lines(x, y_boxes, box_width, box_height, Color::BLACK);

            // Draw channel number
            let text_x = x + box_width/2 - 5;
            let text_y = y_boxes + box_height/2 - 5;

            // Use white text for darker colors, black text for lighter colors
            let text_color = if channel < 8 { Color::WHITE } else { Color::BLACK };
            d.draw_text(&format!("{}", channel), text_x, text_y, 15, text_color);
        }
    }

    // Draw piano keyboard (static method)
    fn draw_keyboard(d: &mut RaylibDrawHandle, active_notes_copy: &HashMap<u8, (u8, u8)>, y_offset: i32) {

        // Draw white keys
        let mut white_key_index = 0;
        for note in KEYBOARD_START_NOTE..=KEYBOARD_END_NOTE {
            if is_white_key(note) {
                let x = white_key_index * WHITE_KEY_WIDTH;
                let y = y_offset;

                // Check if key is active
                let is_active = active_notes_copy.contains_key(&note);
                let fill_color = if is_active {
                    // If active, change color based on channel
                    let (_, channel) = active_notes_copy.get(&note).unwrap();
                    get_channel_color(*channel)
                } else {
                    Color::WHITE
                };

                // Draw white key
                d.draw_rectangle(x, y, WHITE_KEY_WIDTH, WHITE_KEY_HEIGHT, fill_color);
                d.draw_rectangle_lines(x, y, WHITE_KEY_WIDTH, WHITE_KEY_HEIGHT, Color::BLACK);

                // Highlight middle C
                if note == 60 {
                    // Use a light blue color for middle C
                    let middle_c_color = Color::new(135, 206, 235, 255); // Light blue
                    d.draw_rectangle(x, WHITE_KEY_HEIGHT - 10, WHITE_KEY_WIDTH, 10, middle_c_color);
                }

                // Display note number for every octave C
                if note % 12 == 0 {
                    d.draw_text(&format!("{}", note), x + 2, WHITE_KEY_HEIGHT - 15, 8, Color::BLACK);
                }

                white_key_index += 1;
            }
        }

        // Draw black keys
        let mut white_key_index = 0;
        for note in KEYBOARD_START_NOTE..=KEYBOARD_END_NOTE {
            if is_white_key(note) {
                white_key_index += 1;
            } else {
                // Calculate black key position
                let prev_white_key = white_key_index - 1;
                let x = prev_white_key * WHITE_KEY_WIDTH + (WHITE_KEY_WIDTH * 7 / 10);
                let y = y_offset;

                // Check if key is active
                let is_active = active_notes_copy.contains_key(&note);
                let fill_color = if is_active {
                    // If active, change color based on channel
                    let (_, channel) = active_notes_copy.get(&note).unwrap();
                    get_channel_color(*channel)
                } else {
                    Color::BLACK
                };

                // Draw black key
                d.draw_rectangle(x, y, BLACK_KEY_WIDTH, BLACK_KEY_HEIGHT, fill_color);
                d.draw_rectangle_lines(x, y, BLACK_KEY_WIDTH, BLACK_KEY_HEIGHT, Color::WHITE);
            }
        }
    }
}

// Helper functions

// Determine if a note is a white key
fn is_white_key(note: u8) -> bool {
    WHITE_KEYS[(note % 12) as usize]
}

// Get color based on MIDI channel (blue to red gradient)
fn get_channel_color(channel: u8) -> Color {
    // Ensure channel is in range 0-15
    let channel = channel.min(15);

    // Create a blue to red gradient
    // Blue (0, 0, 255) to Red (255, 0, 0)
    let r = (channel as f32 / 15.0 * 255.0) as u8;
    let b = 255 - r;

    Color::new(r, 0, b, 255)
}
