{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Rust toolchain
    rustc
    cargo
    rustfmt
    clippy
    rust-analyzer

    # Audio libraries and dependencies
    alsa-lib
    alsa-lib.dev
    pulseaudio
    jack2
    pipewire

    # GUI dependencies for raylib
    xorg.libX11
    xorg.libXrandr
    xorg.libXi
    xorg.libXcursor
    xorg.libXinerama
    libGL
    libGLU

    # Build tools and system dependencies
    pkg-config
    cmake
    gcc

    # Bindgen dependencies (for raylib-sys)
    llvm
    clang
    libclang.lib

    # Development tools
    gdb
    valgrind

    # Optional: Audio utilities for testing
    pavucontrol
    qjackctl
  ];

  # Environment variables
  shellHook = ''
    echo "🎵 Realtime Audio2MIDI Development Environment"
    echo "=============================================="
    echo "Rust version: $(rustc --version)"
    echo "Cargo version: $(cargo --version)"
    echo ""
    echo "Available audio systems:"
    echo "- ALSA (Advanced Linux Sound Architecture)"
    echo "- PulseAudio"
    echo "- JACK"
    echo "- PipeWire"
    echo ""
    echo "GUI libraries:"
    echo "- Raylib with OpenGL support"
    echo ""
    echo "Development tools:"
    echo "- rust-analyzer (LSP)"
    echo "- clippy (linter)"
    echo "- rustfmt (formatter)"
    echo "- gdb (debugger)"
    echo ""
    echo "Run 'cargo build' to build the project"
    echo "Run 'cargo run' to start the application"
    echo "=============================================="
  '';

  # Library paths for dynamic linking
  LD_LIBRARY_PATH = pkgs.lib.makeLibraryPath (with pkgs; [
    alsa-lib
    pulseaudio
    jack2
    pipewire
    xorg.libX11
    xorg.libXrandr
    xorg.libXi
    xorg.libXcursor
    xorg.libXinerama
    libGL
    libGLU
  ]);

  # PKG_CONFIG_PATH for build-time dependency discovery
  PKG_CONFIG_PATH = pkgs.lib.makeSearchPathOutput "dev" "lib/pkgconfig" (with pkgs; [
    alsa-lib.dev
    xorg.libX11.dev
    xorg.libXrandr.dev
    xorg.libXi.dev
    xorg.libXcursor.dev
    xorg.libXinerama.dev
    libGL.dev
    libGLU.dev
  ]);

  # Additional environment variables for audio development
  ALSA_CARD = "0";  # Default ALSA card
  PULSE_RUNTIME_PATH = "/run/user/$(id -u)/pulse";

  # Bindgen environment variables
  LIBCLANG_PATH = "${pkgs.libclang.lib}/lib";
  BINDGEN_EXTRA_CLANG_ARGS = "-I${pkgs.glibc.dev}/include";
}
